from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.views.generic.dates import DayArchiveView
from django.db.models import Sum, F
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.utils import timezone
import datetime
import json

from .models import DailyRecord, Inventory, Template, Consolidation
from .forms import DailyRecordForm
from .tasks import consolidar_almacen

class DailyRecordListView(ListView):
    model = DailyRecord
    template_name = 'inventory/daily_list.html'
    context_object_name = 'records'

    def get_queryset(self):
        date = self.kwargs.get('date')
        if date:
            try:
                date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
            except ValueError:
                date = datetime.date.today()
        else:
            date = datetime.date.today()
        return DailyRecord.objects.filter(date=date)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        date = self.kwargs.get('date')
        if date:
            try:
                date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
            except ValueError:
                date = datetime.date.today()
        else:
            date = datetime.date.today()
        context['date'] = date
        context['total_ventas'] = self.get_queryset().aggregate(
            total=Sum('venta1')
        )['total'] or 0

        # Calcular total en euros
        context['total_euros'] = self.get_queryset().aggregate(
            total=Sum(F('venta1') * F('precio_venta'))
        )['total'] or 0

        return context

class InventoryListView(ListView):
    model = Inventory
    template_name = 'inventory/inventory_list.html'
    context_object_name = 'items'

class DailyRecordCreateView(CreateView):
    model = DailyRecord
    form_class = DailyRecordForm
    template_name = 'inventory/daily_form.html'
    success_url = reverse_lazy('daily_list')

class DailyRecordUpdateView(UpdateView):
    model = DailyRecord
    form_class = DailyRecordForm
    template_name = 'inventory/daily_form.html'
    success_url = reverse_lazy('daily_list')

class DashboardView(ListView):
    model = DailyRecord
    template_name = 'inventory/dashboard.html'
    context_object_name = 'records'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        today = timezone.now().date()

        # Datos para gráficos - últimos 30 días
        last_30_days = today - datetime.timedelta(days=30)
        context['ventas_por_dia'] = DailyRecord.objects.filter(
            date__gte=last_30_days
        ).values('date').annotate(
            total=Sum(F('venta1') * F('precio_venta'))
        ).order_by('date')

        # Conteo de inventario
        context['inventory_count'] = Inventory.objects.count()

        # Ventas de hoy
        context['ventas_hoy'] = DailyRecord.objects.filter(
            date=today
        ).aggregate(
            total=Sum(F('venta1') * F('precio_venta'))
        )['total'] or 0

        # Ventas del mes
        first_day_of_month = today.replace(day=1)
        context['ventas_mes'] = DailyRecord.objects.filter(
            date__gte=first_day_of_month,
            date__lte=today
        ).aggregate(
            total=Sum(F('venta1') * F('precio_venta'))
        )['total'] or 0

        # Productos con bajo stock
        context['low_stock'] = Inventory.objects.filter(
            stock_actual__lte=15
        ).order_by('stock_actual')

        return context

@method_decorator(csrf_exempt, name='dispatch')
class RefreshStockView(View):
    def post(self, request, *args, **kwargs):
        try:
            # Ejecutar la tarea de consolidación
            consolidar_almacen.delay()
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
