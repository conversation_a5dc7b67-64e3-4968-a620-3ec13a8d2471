{% extends 'base.html' %}

{% block title %}Registros Diarios - Sistema de Inventario{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Registros del día {{ date|date:"d-m-Y" }}</h1>
        <div class="flex space-x-2">
            <a href="{% url 'daily_add' %}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Añadir registro
            </a>
            <form class="flex items-center">
                <input type="date" id="dateSelector" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5" value="{{ date|date:'Y-m-d' }}">
                <button type="button" id="goToDate" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded ml-2">
                    Ir
                </button>
            </form>
        </div>
    </div>

    <div class="bg-gray-800 shadow-md rounded my-6 overflow-hidden">
        <table class="min-w-full bg-gray-800">
            <thead>
                <tr class="bg-gray-700 text-gray-300 uppercase text-sm leading-normal">
                    <th class="py-3 px-6 text-left">Ítem</th>
                    <th class="py-3 px-6 text-center">Quedan</th>
                    <th class="py-3 px-6 text-center">Entrega</th>
                    <th class="py-3 px-6 text-center">Recogida</th>
                    <th class="py-3 px-6 text-center">Venta</th>
                    <th class="py-3 px-6 text-center">Precio (€)</th>
                    <th class="py-3 px-6 text-center">Total (€)</th>
                    <th class="py-3 px-6 text-center">Acciones</th>
                </tr>
            </thead>
            <tbody class="text-gray-400 text-sm">
                {% for record in records %}
                <tr class="border-b border-gray-700 hover:bg-gray-700">
                    <td class="py-3 px-6 text-left">{{ record.item }}</td>
                    <td class="py-3 px-6 text-center">{{ record.quedan }}</td>
                    <td class="py-3 px-6 text-center">{{ record.entrega1 }}</td>
                    <td class="py-3 px-6 text-center">{{ record.recogida1 }}</td>
                    <td class="py-3 px-6 text-center">{{ record.venta1 }}</td>
                    <td class="py-3 px-6 text-center">{{ record.precio_venta }} €</td>
                    <td class="py-3 px-6 text-center">{{ record.total_venta }} €</td>
                    <td class="py-3 px-6 text-center">
                        <a href="{% url 'daily_edit' record.id %}" class="text-blue-600 hover:text-blue-900">Editar</a>
                    </td>
                </tr>
                {% empty %}
                <tr class="border-b border-gray-200">
                    <td colspan="8" class="py-3 px-6 text-center">No hay registros para esta fecha</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr class="bg-gray-700 font-semibold text-gray-300">
                    <td class="py-3 px-6 text-left">TOTAL</td>
                    <td class="py-3 px-6 text-center"></td>
                    <td class="py-3 px-6 text-center"></td>
                    <td class="py-3 px-6 text-center"></td>
                    <td class="py-3 px-6 text-center">{{ total_ventas }}</td>
                    <td class="py-3 px-6 text-center"></td>
                    <td class="py-3 px-6 text-center">
                        {{ total_euros|floatformat:2 }} €
                    </td>
                    <td class="py-3 px-6 text-center"></td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

{% block extra_js %}
<script>
    // Navegación por fecha
    document.getElementById('goToDate').addEventListener('click', function() {
        const selectedDate = document.getElementById('dateSelector').value;
        if (selectedDate) {
            window.location.href = `/inventory/daily/${selectedDate}/`;
        }
    });
</script>
{% endblock %}
{% endblock %}