# Generated by Django 5.2.1 on 2025-05-25 21:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Inventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=100, unique=True)),
                ('stock_actual', models.IntegerField(default=0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Template',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='DailyRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('item', models.CharField(max_length=100)),
                ('quedan', models.IntegerField(default=0)),
                ('entrega1', models.IntegerField(default=0)),
                ('recogida1', models.IntegerField(default=0)),
                ('venta1', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('precio_venta', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
            ],
            options={
                'unique_together': {('date', 'item')},
            },
        ),
        migrations.CreateModel(
            name='Consolidation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('synced_at', models.DateTimeField(auto_now_add=True)),
                ('daily_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.dailyrecord')),
                ('inventory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.inventory')),
            ],
            options={
                'unique_together': {('daily_record', 'inventory')},
            },
        ),
    ]
